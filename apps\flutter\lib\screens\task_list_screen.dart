import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../providers/task_provider.dart';
import '../providers/tailscale_provider.dart';
import '../widgets/task_item.dart';
import '../widgets/add_task_dialog.dart';

/// Main screen showing the list of tasks
class TaskListScreen extends StatelessWidget {
  const TaskListScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Drift Tasks'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          Consumer<TaskProvider>(
            builder: (context, provider, child) {
              return Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Node info
                  Tooltip(
                    message: 'Node ID: ${provider.nodeId}',
                    child: Icon(
                      Icons.device_hub,
                      color: provider.knownNodesCount > 0 
                          ? Colors.green 
                          : Colors.orange,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '${provider.knownNodesCount}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  const SizedBox(width: 16),
                  // Refresh button
                  IconButton(
                    onPressed: provider.isLoading ? null : () {
                      provider.refresh();
                    },
                    icon: provider.isLoading 
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.refresh),
                    tooltip: 'Sync with other nodes',
                  ),
                ],
              );
            },
          ),
        ],
      ),
      body: Consumer2<TaskProvider, TailscaleProvider>(
        builder: (context, taskProvider, tailscaleProvider, child) {
          return RefreshIndicator(
            onRefresh: () async {
              await taskProvider.refresh();
              await tailscaleProvider.refreshDevices();
            },
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Tailscale Configuration Section
                  _buildTailscaleSection(context, tailscaleProvider),
                  const SizedBox(height: 24),

                  // Tasks Section
                  _buildTasksSection(context, taskProvider),
                  const SizedBox(height: 24),

                  // Devices Section
                  if (tailscaleProvider.isConfigured)
                    _buildDevicesSection(context, tailscaleProvider),
                ],
              ),
            ),
          );
        },
      ),
      floatingActionButton: Consumer<TaskProvider>(
        builder: (context, provider, child) {
          return FloatingActionButton(
            onPressed: provider.isLoading ? null : () {
              _showAddTaskDialog(context);
            },
            tooltip: 'Add Task',
            child: const Icon(Icons.add),
          );
        },
      ),
    );
  }

  void _showAddTaskDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const AddTaskDialog(),
    );
  }

  Widget _buildTailscaleSection(BuildContext context, TailscaleProvider provider) {
    final tailnetController = TextEditingController(text: provider.tailnet);
    final tokenController = TextEditingController();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Tailscale Configuration',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: tailnetController,
              decoration: const InputDecoration(
                labelText: 'Tailnet',
                hintText: 'your-tailnet.ts.net',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 12),
            TextField(
              controller: tokenController,
              decoration: InputDecoration(
                labelText: 'Access Token',
                hintText: provider.tokenMasked.isEmpty ? 'tskey-...' : provider.tokenMasked,
                border: const OutlineInputBorder(),
              ),
              obscureText: true,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                ElevatedButton(
                  onPressed: provider.isLoading ? null : () async {
                    if (tailnetController.text.isNotEmpty && tokenController.text.isNotEmpty) {
                      await provider.updateConfiguration(
                        tailnetController.text,
                        tokenController.text,
                      );
                    }
                  },
                  child: provider.isLoading
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Text('Save'),
                ),
                const SizedBox(width: 8),
                if (provider.isConfigured)
                  OutlinedButton(
                    onPressed: provider.isLoading ? null : () async {
                      await provider.refreshDevices();
                    },
                    child: const Text('Refresh Devices'),
                  ),
              ],
            ),
            if (provider.error != null) ...[
              const SizedBox(height: 8),
              Text(
                provider.error!,
                style: TextStyle(color: Theme.of(context).colorScheme.error),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTasksSection(BuildContext context, TaskProvider provider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Tasks',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                IconButton(
                  onPressed: () => _showAddTaskDialog(context),
                  icon: const Icon(Icons.add),
                  tooltip: 'Add Task',
                ),
              ],
            ),
            const SizedBox(height: 8),
            if (provider.error != null) ...[
              Text(
                'Error: ${provider.error}',
                style: TextStyle(color: Theme.of(context).colorScheme.error),
              ),
              const SizedBox(height: 8),
            ],
            if (provider.tasks.isEmpty && !provider.isLoading)
              const Padding(
                padding: EdgeInsets.symmetric(vertical: 16),
                child: Text('No tasks yet. Tap + to add your first task.'),
              )
            else
              ...provider.tasks.map((task) => Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: TaskItem(
                  task: task,
                  onCompleted: () => provider.completeTask(task.id),
                ),
              )),
          ],
        ),
      ),
    );
  }

  Widget _buildDevicesSection(BuildContext context, TailscaleProvider provider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Connected Devices',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            if (provider.devices.isEmpty)
              const Text('No devices found')
            else
              ...provider.devices.map((device) => ListTile(
                leading: Icon(
                  device.isOnline ? Icons.computer : Icons.computer_outlined,
                  color: device.isOnline ? Colors.green : Colors.grey,
                ),
                title: Text(device.name),
                subtitle: Text('${device.ipAddress}:${device.port}'),
                trailing: device.isOnline
                    ? const Icon(Icons.circle, color: Colors.green, size: 12)
                    : const Icon(Icons.circle, color: Colors.grey, size: 12),
              )),
          ],
        ),
      ),
    );
  }
}
