import 'dart:async';
import 'dart:convert';

import 'package:core/core.dart';
import 'package:http/http.dart' as http;

/// Client for interacting with Tailscale API
class TailscaleClient {
  final String? _accessToken;
  final String? _tailnet;

  static const String _baseUrl = Configuration.tailscaleApiBaseUrl;

  TailscaleClient({
    String? accessToken,
    String? tailnet,
  })  : _accessToken = accessToken,
        _tailnet = tailnet;

  /// Get all devices in the tailnet
  Future<List<Node>> getDevices() async {
    if (_accessToken == null || _tailnet == null) {
      throw StateError('Tailscale access token and tailnet must be configured');
    }

    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/tailnet/$_tailnet/devices'),
        headers: {
          'Authorization': 'Bearer $_accessToken',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode != 200) {
        throw Exception(
            'Failed to get devices: ${response.statusCode} ${response.body}');
      }

      final data = jsonDecode(response.body) as Map<String, dynamic>;
      final devices = data['devices'] as List<dynamic>;

      return devices.map((device) => Node.fromTailscaleDevice(device)).toList();
    } catch (e) {
      throw Exception('Failed to fetch Tailscale devices: $e');
    }
  }

  /// Check if a specific device is online
  Future<bool> isDeviceOnline(String deviceId) async {
    if (_accessToken == null || _tailnet == null) {
      return false;
    }

    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/tailnet/$_tailnet/devices/$deviceId'),
        headers: {
          'Authorization': 'Bearer $_accessToken',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode != 200) {
        return false;
      }

      final data = jsonDecode(response.body) as Map<String, dynamic>;
      return data['online'] as bool? ?? false;
    } catch (e) {
      return false;
    }
  }

  /// Get device information by ID
  Future<Node?> getDevice(String deviceId, {int defaultPort = 8080}) async {
    if (_accessToken == null || _tailnet == null) {
      return null;
    }

    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/tailnet/$_tailnet/devices/$deviceId'),
        headers: {
          'Authorization': 'Bearer $_accessToken',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode != 200) {
        return null;
      }

      final data = jsonDecode(response.body) as Map<String, dynamic>;
      return Node.fromTailscaleDevice(data, defaultPort);
    } catch (e) {
      return null;
    }
  }

  /// Test the connection to Tailscale API
  Future<bool> testConnection() async {
    if (_accessToken == null || _tailnet == null) {
      return false;
    }

    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/tailnet/$_tailnet'),
        headers: {
          'Authorization': 'Bearer $_accessToken',
          'Content-Type': 'application/json',
        },
      );

      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  /// Extract tailnet from access token or use provided tailnet
  static String? extractTailnet(String? accessToken, String? providedTailnet) {
    if (providedTailnet != null && providedTailnet.isNotEmpty) {
      return providedTailnet;
    }

    // For now, return null if no tailnet is provided
    // In a real implementation, you might try to extract it from the token
    // or make an API call to get the user's tailnets
    return null;
  }
}
