{"buildFiles": ["D:\\sdk\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\code\\drift\\apps\\flutter\\build\\.cxx\\Debug\\5h6s4l5w\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\code\\drift\\apps\\flutter\\build\\.cxx\\Debug\\5h6s4l5w\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}