import 'package:core/core.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'providers/task_provider.dart';
import 'providers/tailscale_provider.dart';
import 'screens/task_list_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Drift core
  final driftCore = Core();
  await driftCore.initialize(
    // You can configure Tailscale here if needed
    // tailscaleAccessToken: 'your-token-here',
  );

  runApp(DriftApp(driftCore: driftCore));
}

class DriftApp extends StatelessWidget {
  final Core driftCore;

  const DriftApp({super.key, required this.driftCore});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => TaskProvider(driftCore)),
        ChangeNotifierProvider(create: (context) => TailscaleProvider(driftCore)),
      ],
      child: MaterialApp(
        title: 'Drift - Distributed Tasks',
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
          useMaterial3: true,
        ),
        home: const TaskListScreen(),
      ),
    );
  }
}
