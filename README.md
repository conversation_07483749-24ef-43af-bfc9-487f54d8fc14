# Drift

Drift is a distributed system built with Flutter that enables task management across multiple devices using event sourcing and peer-to-peer synchronization via Tailscale.

## Project Structure

```
apps/
├── core/                      # Dart package with core logic
│   └── lib/src/
│       ├── database/          # SQLite management and event store
│       ├── api/               # HTTP API server
│       ├── sync/              # Tailscale integration and sync logic
│       └── models/            # Data models
└── flutter/                   # Flutter application (Android, Windows, Linux, iOS)
    └── lib/
        ├── screens/           # UI screens
        ├── providers/         # State management
        └── main.dart
scripts/                       # Setup and build scripts
```

## Getting Started

### Prerequisites

- Flutter SDK 3.22+
- Tailscale account (optional)

### Setup

```bash
./setup.sh
```

## Architecture

- **Event Sourcing**: All changes stored as immutable events in SQLite
- **Distributed Sync**: Nodes discover and sync via Tailscale API
- **Local-First**: Each device operates autonomously
- **HTTP API**: REST endpoints for peer communication
