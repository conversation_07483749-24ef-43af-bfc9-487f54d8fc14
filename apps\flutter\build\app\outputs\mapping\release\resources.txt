Marking id:tag_window_insets_animation_callback:2131165266 reachable: referenced from D:\code\drift\apps\flutter\build\app\intermediates\dex\release\minifyReleaseWithR8\classes.dex
Marking id:tag_on_apply_window_listener:2131165258 reachable: referenced from D:\code\drift\apps\flutter\build\app\intermediates\dex\release\minifyReleaseWithR8\classes.dex
Marking string:androidx_startup:2131427328 reachable: referenced from D:\code\drift\apps\flutter\build\app\intermediates\dex\release\minifyReleaseWithR8\classes.dex
android.content.res.Resources#getIdentifier present: true
Web content present: false
Referenced Strings:
exception
cancel
DISMISS
TextInput.requestAutofill
app_flutter
deviceId
java.lang.Integer
deleteDatabase
music
verticalText
PICTURES
verificationMode
android.util.LongArray
getUncaughtExceptionPreHandler
onBackPressed
GeneratedPluginsRegister
java.lang.CharSequence
_handled
givenName
click
kotlinx.coroutines.DefaultExecutor
left
kotlin.jvm.functions.
kotlinx.coroutines.scheduler.default....
TextInputClient.updateEditingStateWit...
registerWith
SystemSoundType.alert
S_RESUMING_BY_RCV
DECREASE
touchOffset
android.intent.extra.PROCESS_TEXT_REA...
libapp.so
result
RestorationChannel
middleInitial
SystemUiMode.immersiveSticky
flutter/platform_views
TextCapitalization.words
codePoint
EXTRA_BENCHMARK_OPERATION
_
a
b
c
SystemUiOverlay.top
f
RESULT_PARSE_EXCEPTION
destroy_engine_with_activity
kotlin.String
RESUMING_BY_EB
sidecarDeviceState
o
java.lang.Module
SUSPEND
this$0
x
onNewIntent
SystemUiMode.edgeToEdge
Startup
acc
flutter_assets
TextInputClient.performPrivateCommand
displayFeature.rect
androidx.profileinstaller.action.SKIP...
mimeType
isTagEnabled
startIndex
emailAddress
addSuppressed
bufferEndSegment
addressCountry
LONG_PRESS
DID_LOSE_ACCESSIBILITY_FOCUS
flutter/keyevent
phoneCountryCode
telephoneNumberCountryCode
databaseExists
decimal
AppLifecycleState.
SystemChrome.setSystemUIOverlayStyle
addWindowLayoutInfoListener
TextInput.setEditableSizeAndTransform
ROOT
COMPLETING_WAITING_CHILDREN
enable_state_restoration
RESULT_BASELINE_PROFILE_NOT_FOUND
_invoked
locale
RESULT_DELETE_SKIP_FILE_SUCCESS
SDK_INT
KeyEmbedderResponder
iterator.baseContext
SET_SELECTION
kotlinx.coroutines.main.delay
_delayed
io.flutter.embedding.android.OldGenHe...
BITMAP_MASKABLE
MOVE_CURSOR_BACKWARD_BY_CHARACTER
dev.flutter.pigeon.path_provider_andr...
kotlin.collections.List
label
MOVE_CURSOR_FORWARD_BY_CHARACTER
message
scanCode
FlutterJNI
args
java.util.function.Consumer
resizeUpLeft
setDisplayFeatures
HapticFeedbackType.lightImpact
TextInputType.emailAddress
creditCardExpirationDay
ACTION_ARGUMENT_SET_TEXT_CHARSEQUENCE
android.view.View$AttachInfo
username
FlutterActivity
androidThreadCount
java.lang.Float
Dispatchers.IO
UNDEFINED
focus
PAUSED
androidx.profileinstaller.action.SAVE...
android.os.Build$VERSION
dexopt/baseline.prof
executor
androidx.window.extensions.WindowExte...
kotlinx.coroutines.internal.StackTrac...
createSegment
write
onStop
TextInputType.none
TextInputAction.none
byte
_rootCause
resizeUp
creditCardNumber
RESULT_NOT_WRITABLE
onPostResume
cmd
io.flutter.Entrypoint
RESULT_INSTALL_SKIP_FILE_SUCCESS
other
wait
FlutterActivityAndFragmentDelegate
birthDateYear
android.intent.extra.TEXT
cell
URI
FlutterTextureView
top
io.flutter.embedding.android.EnableVu...
kotlin.Long
com.tekartik.sqflite
Completing
java.lang.String
NOTIFICATIONS
addressRegion
TextInput.setClient
resizeDown
_resumed
inTransaction
TextInputType.multiline
getChildId
framework
runningWorkers
noResult
DESTROYED
New
selectionExtent
SystemChrome.setEnabledSystemUIOverlays
kotlin.collections.Collection
ReflectionGuard
TextInputAction.send
onStart
android.widget.CheckBox
_isCompleting
sqlite_error
HapticFeedback.vibrate
repeatCount
noDrop
flutter/keydata
void
read
memoryPressure
flutter/restoration
touch
onUserLeaveHint
mStableInsets
_cur
onResume
java.util.List
hybrid
kotlin.Int
basic
kotlin.Throwable
parkedWorkersStack
handleLifecycleEvent
RINGTONES
COMPLETING_ALREADY
systemNavigationBarColor
displayCutout
PlatformPlugin
java.lang.Iterable
kotlinx.coroutines.DefaultExecutor.ke...
kotlin.Annotation
synchronizeToNativeViewHierarchy
info.displayFeatures
direction
BITMAP
windowConfiguration
rows
resizeUpRight
Array
androidx.window.extensions.layout.Win...
getDatabasesPath
flutter/lifecycle
TextInputType.number
UNKNOWN
getParentNodeId
accessibility
getAppBounds
SystemChrome.setEnabledSystemUIMode
ringtones
android.widget.HorizontalScrollView
receiveSegment
password
kotlinx.coroutines.scheduler.keep.ali...
NO_CLOSE_CAUSE
INCREASE
state
CompanionObject
element
cache
creditCardExpirationMonth
deltaText
zoomIn
android.view.ViewRootImpl
io.flutter.embedding.android.EnableSu...
_COROUTINE.
REMOVE_FROZEN
getStateMethod
DROP_SHADER_CACHE
kotlin.collections.ListIterator
oemFeature.bounds
getKeyboardState
CUSTOM_ACTION
TextInputClient.updateEditingStateWit...
cached_engine_id
setRemoveOnCancelPolicy
MUSIC
receive
forbidden
windowToken
blockingTasksInBuffer
onTrimMemory
getSourceNodeId
recovered
streetAddress
kotlin.Char
ACTION_ARGUMENT_EXTEND_SELECTION_BOOLEAN
flutter/isolate
grab
_decisionAndIndex
dimen
lib
java.util.ListIterator
documents
dev.flutter.pigeon.path_provider_andr...
flutter/backgesture
DefaultDispatcher
source
kotlin.Double
onActivityResult
removeListenerMethod
view
java.lang.Short
allScroll
SystemChrome.systemUIChange
android.widget.Button
_closeCause
addressCity
includeSubdomains
unreachable
batch
creditCardSecurityCode
DIAGNOSTIC_PROFILE_IS_COMPRESSED
FULL
flutter
phoneNumber
composingBase
name
DartExecutor
fields
INTERRUPTED_RCV
getDescriptor
keymap
string
FlutterLoader
onRequestPermissionsResult
deltaStart
kotlin.coroutines.jvm.internal.BaseCo...
android
platformViewId
java.lang.module.ModuleDescriptor
description
nameSuffix
_isTerminated
SEALED
CREATED
textScaleFactor
namePrefix
io.flutter.embedding.android.NormalTheme
SUSPEND_NO_WAITER
Dispatchers.Main
EXTRA_SKIP_FILE_OPERATION
_windowInsetsCompat
FlutterSurfaceView
Cancelled
getPosture
pair
getWindowExtensions
enableSuggestions
PASTE
short
middleName
window
personFamilyName
Cancelling
COMPLETING_RETRY
TextInput.clearClient
SCROLL_RIGHT
_isCompleted
android.widget.ImageView
android.widget.RadioButton
textCapitalization
hybridFallback
getBounds
put
EmptyCoroutineContext
TextInputType.text
ACTION_ARGUMENT_SELECTION_END_INT
FAILED
POISONED
SCROLL_LEFT
options
HapticFeedbackType.heavyImpact
TextInputAction.search
flutter/platform
dart_entrypoint
kotlinx.coroutines.scheduler.core.poo...
kotlinx.coroutines.scheduler.max.pool...
double
newPassword
smsOTPCode
closeHandler
flutter_deeplinking_enabled
removeWindowLayoutInfoListener
BUFFERED
light
kotlin.Short
.apk
getDisplayFeatures
CHANNEL_CLOSED
java.lang.Throwable
cursorPageSize
/data/misc/profiles/cur/0/
flags
SystemSound.play
onPause
SystemUiOverlay.bottom
ImageReaderSurfaceProducer
enabled
kotlinx.coroutines.bufferedChannel.ex...
android.intent.action.RUN
android.widget.SeekBar
INITIALIZED
mContentInsets
android.support.v13.view.inputmethod....
KeyEventChannel
resuming_sender
producerIndex
flutter/settings
stackTrace
addressLocality
setDirection
java.util.Map$Entry
display
SpellCheck.initiateSpellCheck
composingExtent
DeviceOrientation.landscapeLeft
birthDateDay
.immediate
TextInputAction.unspecified
libflutter.so
char
width
sendSegment
_parentHandle
kotlinx.coroutines.fast.service.loader
TAP
completedExpandBuffersAndPauseFlag
insets
kotlin.Any
Brightness.dark
RESULT_INSTALL_SUCCESS
SettingsChannel
selectionBase
wm.maximumWindowMetrics.bounds
plainCodePoint
getWindowLayoutComponent
_reusableCancellableContinuation
android.view.View
TextInputAction.commitContent
inputType
java.lang.Byte
CLOSE_HANDLER_INVOKED
java.lang.Cloneable
deltaEnd
kind
GeneratedPluginRegistrant
io.flutter.embedding.android.EnableIm...
_consensus
CANCELLED
SCROLL_UP
CLOSED_EMPTY
kotlin.collections.Iterator
TextInputClient.requestExistingInputS...
setLocale
Clipboard.setData
TextInput.sendAppPrivateCommand
wm.defaultDisplay
TextInputType.phone
SET_TEXT
CLOSED
SystemChrome.restoreSystemUIOverlays
unexpected
NO_RECEIVE_RESULT
insert
androidSetLocale
consumerIndex
continueOnError
kotlin.Enum
_removedRef
alias
deltas
kotlin.String.Companion
uniqueIdentifier
move
debug
alarms
jClass
TextInput.show
setPosture
WindowInsetsCompat
android.intent.action.PROCESS_TEXT
systemNavigationBarDividerColor
mAttachInfo
suggestions
classes.dex
extent
getModule
sidecarCompat
resizeColumn
plugins
kotlin.Cloneable
tooltip
PlatformViewsController2
PlatformViewsController
path
flutter/keyboard
systemStatusBarContrastEnforced
androidx.lifecycle.LifecycleDispatche...
getSuppressed
kotlin.reflect.jvm.internal.Reflectio...
FOLD
DartMessenger
cleanedAndPointers
databases
addObserver
ON_ANY
SHOULD_BUFFER
guava.concurrent.generate_cancellatio...
SystemNavigator.setFrameworkHandlesBack
MOVE_CURSOR_BACKWARD_BY_WORD
kotlinx.coroutines.scheduler.resoluti...
kotlin.Unit
route
ON_PAUSE
getType
birthdayDay
domain
kotlin.jvm.internal.StringCompanionOb...
viewType
editingValue
getPlatformVersion
SidecarCompat
:memory:
DROP_LATEST
_exceptionsHolder
transparent
java.
io.flutter.embedding.android.Impeller...
SystemSoundType.click
resizeUpDown
logLevel
background_mode
popRoute
defaultDisplay
androidx.window.extensions.WindowExte...
RESULT_DESIRED_FORMAT_UNSUPPORTED
clearFocus
flutter/scribe
hintText
INACTIVE
personNamePrefix
enableOnBackInvokedCallbackState
toString
feature.rect
kotlin.Boolean
_queue
setSidecarCallback
configurationId
DOWNLOADS
info
MOVIES
SCROLL_TO_OFFSET
Failed
NonDisposableHandle
PODCASTS
TextInputType.name
recoveredInTransaction
navigation_bar_height
java.lang.annotation.Annotation
countryName
FlutterImageView
nodeId
inputAction
TERMINATED
Localization.getStringResource
flutter/navigation
extendedAddress
DOCUMENTS
wm.currentWindowMetrics.bounds
content
kotlin.collections.Map
cached_engine_group_id
ProcessText.queryTextActions
statusBarColor
hashCode
ProfileInstaller
updateBackGestureProgress
java.lang.Boolean
contentCommitMimeTypes
classSimpleName
DeviceOrientation.landscapeRight
Scribe.isStylusHandwritingAvailable
os.arch
IconCompat
FlutterEngineCxnRegstry
false
keydown
workerCtl
activateSystemCursor
TextInputClient.updateEditingState
flutter/accessibility
obj
resizeLeft
io.flutter.embedding.android.LeakVM
pushRouteInformation
resize
androidx.window.extensions.layout.Fol...
TextEditingDelta
setInitialRoute
TextInputAction.next
DROP_OLDEST
lastScheduledTask
clipboard
birthdayMonth
com.tekartik.sqflite.wal_enabled
autocorrect
HINGE
java.lang.Character
HapticFeedbackType.selectionClick
endIndex
context
action
text
id
birthdayYear
TextInput.finishAutofillContext
primary.prof
io.flutter.embedding.android.EnableOp...
signed
params
UNDECIDED
FlutterView
announce
PLAIN_TEXT
TextInputType.address
getResId
TextInputType.url
kotlin.jvm.internal.
MOVE_CURSOR_FORWARD_BY_WORD
fullStreetAddress
arch_disk_io_
telephoneNumber
cancelBackGesture
cursorId
bundle
androidThreadPriority
onDestroy
personNameSuffix
kotlin.jvm.functions.Function
dev.flutter.pigeon.path_provider_andr...
uri
resizeLeftRight
platformBrightness
TextInputAction.done
android.widget.ScrollView
queryCursorNext
equals
onSaveInstanceState
io.flutter.EntrypointUri
/data/misc/profiles/ref/
resizeUpLeftDownRight
KeyboardManager
RESUMED
flutter/processtext
newUsername
main
flutter/platform_views_2
commitBackGesture
_size
mVisibleInsets
personMiddleInitial
cleartextTrafficPermitted
DID_GAIN_ACCESSIBILITY_FOCUS
kotlin.Number
DeviceOrientation.portraitDown
TOO_LATE_TO_CANCEL
NULL
birthDateMonth
ListenableEditingState
personMiddleName
io.flutter.embedding.android.Impeller...
null
grabbing
alwaysUse24HourFormat
true
dispose
phoneNational
TextInputType.visiblePassword
p0
birthday
code
statusBarIconBrightness
dcim
sendersAndCloseStatus
sql
asyncTraceEnd
getWindowLayoutComponentMethod
transform
receivers
STARTED
kotlin.Comparable
dev.flutter.pigeon.path_provider_andr...
show_password
consumer
baseKey
nativeSpellCheckServiceDefined
DONE_RCV
currentDisplay
Clipboard.getData
longPress
TextInputType.twitter
COROUTINE_SUSPENDED
dev.flutter.pigeon.path_provider_andr...
Trace
Share.invoke
androidx.core.view.inputmethod.Editor...
flutter/textinput
Completed
callback
RESUME_TOKEN
DCIM
ProcessText.processTextAction
PathProviderPlugin
io.flutter.embedding.android.DisableM...
TextInput.hide
createAsync
.Companion
telephoneNumberNational
character
java.lang.Long
metaState
java.util.Map
TRACE_TAG_APP
/data/misc/profiles/cur/0
height
BOTTOM_OVERLAYS
CUT
$this$require
readOnly
execute
kotlin.Array
transactionId
accept
input_method
oldText
Clipboard.hasStrings
CONDITION_FALSE
activity
kotlin.Function
long
SystemUiMode.immersive
startBackGesture
HALF_OPENED
kotlinx.coroutines.channels.defaultBu...
ImageTextureRegistryEntry
key
email
android.type.verbatim
kotlin.Enum.Companion
RESOURCE
profileinstaller_profileWrittenFor_la...
creditCardExpirationDate
obscureText
WRITE_SKIP_FILE
kotlin.Float
androidx.core.view.inputmethod.Editor...
systemNavigationBarIconBrightness
contextMenu
handled
resizeRight
SystemNavigator.pop
progress
compressed
TextInputAction.newline
autofill
io.flutter.embedding.android.EnableVu...
TextCapitalization.none
_prev
android.widget.EditText
NO_DECISION
Scribe.startStylusHandwriting
loader
postalCode
primaryColor
personGivenName
TextInput.setEditingState
androidx.profileinstaller.action.INST...
ON_START
birthDateFull
TOP_OVERLAYS
Dispatchers.Default
kotlinx.coroutines.io.parallelism
query
java.util.Collection
HIDDEN
CLOSE_HANDLER_CLOSED
android.view.DisplayInfo
postalAddressExtendedPostalCode
profileInstalled
NOT_IN_STACK
TextInputAction.previous
_next
debugMode
destination
ON_DESTROY
update
enableDeltaModel
RESULT_ALREADY_INSTALLED
closeDatabase
getTypeMethod
flutter/mousecursor
downloads
textservices
text/plain
creditCardExpirationYear
flutter/system
SystemUiMode.leanBack
onWindowLayoutChangeListenerRemoved
Dispatchers.Main.immediate
Scribe.isFeatureAvailable
isSurfaceControlEnabled
location
java.lang.Object
TextInputType.webSearch
SystemChrome.setSystemUIChangeListener
base
dexopt/baseline.profm
TextInput.setPlatformViewClient
kotlinx.coroutines.bufferedChannel.se...
extendedPostalCode
android.intent.extra.PROCESS_TEXT
prefix
zoomOut
none
type
state1
actionLabel
movies
TextCapitalization.sentences
getViewRootImpl
delimiter
HapticFeedbackType.mediumImpact
NONE
openDatabase
cont
Sqflite
ACTION_ARGUMENT_MOVEMENT_GRANULARITY_INT
DeviceOrientation.portraitUp
method
CONSUMED
hints
systemNavigationBarContrastEnforced
onWindowLayoutChangeListenerAdded
keyup
java.util.Set
tekartik_sqflite.db
ACTION_ARGUMENT_SELECTION_START_INT
push
_state
SHOW_ON_SCREEN
bad_param
brieflyShowPassword
notifications
newDeviceState
DELETE_SKIP_FILE
gender
java.lang.Double
columns
FOCUS
resizeRow
addListenerMethod
flutter/spellcheck
usesVirtualDisplay
pictures
feature
NO_THREAD_ELEMENTS
get
INTERRUPTED_SEND
dark
oneTimeCode
FLAT
singleInstance
getDisplayInfo
copy
precise
event
java.lang.Number
resizeDownRight
newLayout
COPY
PENDING
androidx.profileinstaller.action.BENC...
SystemChrome.setPreferredOrientations
help
phoneNumberDevice
podcasts
flutter/deferredcomponent
outState
elements
java.lang.Comparable
ALARMS
URI_MASKABLE
ON_CREATE
android.widget.Switch
data
resizeUpRightDownLeft
ON_RESUME
float
getWindowExtensionsMethod
postalAddressExtended
TextCapitalization.characters
asyncTraceBegin
java.lang.Enum
enableIMEPersonalizedLearning
familyName
create
DETACHED
TextInputType.datetime
TextInputAction.go
tap
offset
SystemChrome.setApplicationSwitcherDe...
kotlin.jvm.internal.EnumCompanionObject
RESULT_UNSUPPORTED_ART_VERSION
io.flutter.InitialRoute
controlState
transition_animation_scale
keyCode
swipeEdge
Active
postalAddress
kotlin.collections.Iterable
telephoneNumberDevice
DATA
UTF8
files
flutter/localization
didGainFocus
send
mChildNodeIds
kotlin.collections.Map.Entry
Brightness.light
error
getBoundsMethod
kotlin.Byte
io.flutter.embedding.android.Impeller...
bufferEnd
operations
array
kotlin.collections.Set
postfix
android.intent.action.SEND
value
getWindowLayoutInfo
REUSABLE_CLAIMED
opaque
java.util.Iterator
0x%08x
dart_entrypoint_args
onWindowFocusChanged
TextInputClient.performAction
ON_STOP
addressState
Parcelizer
kotlin.CharSequence
RESULT_IO_EXCEPTION
int
personName
getState
boolean
com.google.android.inputmethod.latin
arguments
AccessibilityBridge
SCROLL_DOWN
Marking id:accessibility_action_clickable_span:2131165184 used because it matches string pool constant acc
Marking id:accessibility_custom_action_0:2131165185 used because it matches string pool constant acc
Marking id:accessibility_custom_action_1:2131165186 used because it matches string pool constant acc
Marking id:accessibility_custom_action_10:2131165187 used because it matches string pool constant acc
Marking id:accessibility_custom_action_11:2131165188 used because it matches string pool constant acc
Marking id:accessibility_custom_action_12:2131165189 used because it matches string pool constant acc
Marking id:accessibility_custom_action_13:2131165190 used because it matches string pool constant acc
Marking id:accessibility_custom_action_14:2131165191 used because it matches string pool constant acc
Marking id:accessibility_custom_action_15:2131165192 used because it matches string pool constant acc
Marking id:accessibility_custom_action_16:2131165193 used because it matches string pool constant acc
Marking id:accessibility_custom_action_17:2131165194 used because it matches string pool constant acc
Marking id:accessibility_custom_action_18:2131165195 used because it matches string pool constant acc
Marking id:accessibility_custom_action_19:2131165196 used because it matches string pool constant acc
Marking id:accessibility_custom_action_2:2131165197 used because it matches string pool constant acc
Marking id:accessibility_custom_action_20:2131165198 used because it matches string pool constant acc
Marking id:accessibility_custom_action_21:2131165199 used because it matches string pool constant acc
Marking id:accessibility_custom_action_22:2131165200 used because it matches string pool constant acc
Marking id:accessibility_custom_action_23:2131165201 used because it matches string pool constant acc
Marking id:accessibility_custom_action_24:2131165202 used because it matches string pool constant acc
Marking id:accessibility_custom_action_25:2131165203 used because it matches string pool constant acc
Marking id:accessibility_custom_action_26:2131165204 used because it matches string pool constant acc
Marking id:accessibility_custom_action_27:2131165205 used because it matches string pool constant acc
Marking id:accessibility_custom_action_28:2131165206 used because it matches string pool constant acc
Marking id:accessibility_custom_action_29:2131165207 used because it matches string pool constant acc
Marking id:accessibility_custom_action_3:2131165208 used because it matches string pool constant acc
Marking id:accessibility_custom_action_30:2131165209 used because it matches string pool constant acc
Marking id:accessibility_custom_action_31:2131165210 used because it matches string pool constant acc
Marking id:accessibility_custom_action_4:2131165211 used because it matches string pool constant acc
Marking id:accessibility_custom_action_5:2131165212 used because it matches string pool constant acc
Marking id:accessibility_custom_action_6:2131165213 used because it matches string pool constant acc
Marking id:accessibility_custom_action_7:2131165214 used because it matches string pool constant acc
Marking id:accessibility_custom_action_8:2131165215 used because it matches string pool constant acc
Marking id:accessibility_custom_action_9:2131165216 used because it matches string pool constant acc
Marking id:locale:2131165242 used because it matches string pool constant locale
Marking id:topToBottom:2131165271 used because it matches string pool constant top
Marking id:accessibility_action_clickable_span:2131165184 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_0:2131165185 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_1:2131165186 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_10:2131165187 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_11:2131165188 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_12:2131165189 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_13:2131165190 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_14:2131165191 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_15:2131165192 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_16:2131165193 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_17:2131165194 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_18:2131165195 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_19:2131165196 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_2:2131165197 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_20:2131165198 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_21:2131165199 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_22:2131165200 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_23:2131165201 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_24:2131165202 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_25:2131165203 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_26:2131165204 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_27:2131165205 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_28:2131165206 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_29:2131165207 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_3:2131165208 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_30:2131165209 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_31:2131165210 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_4:2131165211 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_5:2131165212 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_6:2131165213 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_7:2131165214 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_8:2131165215 used because it matches string pool constant accessibility
Marking id:accessibility_custom_action_9:2131165216 used because it matches string pool constant accessibility
Marking id:view_tree_lifecycle_owner:2131165272 used because it matches string pool constant view
Marking id:view_tree_on_back_pressed_dispatcher_owner:2131165273 used because it matches string pool constant view
Marking id:view_tree_saved_state_registry_owner:2131165274 used because it matches string pool constant view
Marking id:view_tree_view_model_store_owner:2131165275 used because it matches string pool constant view
Marking color:androidx_core_ripple_material_light:2130968576 used because it matches string pool constant android
Marking color:androidx_core_secondary_text_default_material_light:2130968577 used because it matches string pool constant android
Marking id:androidx_window_activity_scope:2131165226 used because it matches string pool constant android
Marking string:androidx_startup:2131427328 used because it matches string pool constant android
Marking attr:shortcutMatchRequired:2130903067 used because it matches string pool constant short
Marking id:info:2131165238 used because it matches string pool constant info
Marking id:action_container:2131165217 used because it matches string pool constant action
Marking id:action_divider:2131165218 used because it matches string pool constant action
Marking id:action_image:2131165219 used because it matches string pool constant action
Marking id:action_text:2131165220 used because it matches string pool constant action
Marking id:actions:2131165221 used because it matches string pool constant action
Marking id:text:2131165267 used because it matches string pool constant text
Marking id:text2:2131165268 used because it matches string pool constant text
Marking attr:activityAction:2130903040 used because it matches string pool constant activity
Marking attr:activityName:2130903041 used because it matches string pool constant activity
Marking attr:queryPatterns:2130903064 used because it matches string pool constant query
@anim/fragment_fast_out_extra_slow_in : reachable=false
@animator/fragment_close_enter : reachable=false
    @anim/fragment_fast_out_extra_slow_in
@animator/fragment_close_exit : reachable=false
    @anim/fragment_fast_out_extra_slow_in
@animator/fragment_fade_enter : reachable=false
@animator/fragment_fade_exit : reachable=false
@animator/fragment_open_enter : reachable=false
    @anim/fragment_fast_out_extra_slow_in
@animator/fragment_open_exit : reachable=false
    @anim/fragment_fast_out_extra_slow_in
@attr/activityAction : reachable=true
@attr/activityName : reachable=true
@attr/alpha : reachable=false
@attr/alwaysExpand : reachable=false
@attr/animationBackgroundColor : reachable=false
@attr/clearTop : reachable=false
@attr/finishPrimaryWithPlaceholder : reachable=false
@attr/finishPrimaryWithSecondary : reachable=false
@attr/finishSecondaryWithPrimary : reachable=false
@attr/font : reachable=false
@attr/fontProviderAuthority : reachable=false
@attr/fontProviderCerts : reachable=false
@attr/fontProviderFetchStrategy : reachable=false
@attr/fontProviderFetchTimeout : reachable=false
@attr/fontProviderPackage : reachable=false
@attr/fontProviderQuery : reachable=false
@attr/fontProviderSystemFontFamily : reachable=false
@attr/fontStyle : reachable=false
@attr/fontVariationSettings : reachable=false
@attr/fontWeight : reachable=false
@attr/lStar : reachable=false
@attr/nestedScrollViewStyle : reachable=false
@attr/placeholderActivityName : reachable=false
@attr/primaryActivityName : reachable=false
@attr/queryPatterns : reachable=true
@attr/secondaryActivityAction : reachable=false
@attr/secondaryActivityName : reachable=false
@attr/shortcutMatchRequired : reachable=true
@attr/splitLayoutDirection : reachable=false
@attr/splitMaxAspectRatioInLandscape : reachable=false
@attr/splitMaxAspectRatioInPortrait : reachable=false
@attr/splitMinHeightDp : reachable=false
@attr/splitMinSmallestWidthDp : reachable=false
@attr/splitMinWidthDp : reachable=false
@attr/splitRatio : reachable=false
@attr/stickyPlaceholder : reachable=false
@attr/tag : reachable=false
@attr/ttcIndex : reachable=false
@color/androidx_core_ripple_material_light : reachable=true
@color/androidx_core_secondary_text_default_material_light : reachable=true
@color/call_notification_answer_color : reachable=false
@color/call_notification_decline_color : reachable=false
@color/notification_action_color_filter : reachable=false
    @color/androidx_core_secondary_text_default_material_light
@color/notification_icon_bg_color : reachable=false
@dimen/compat_button_inset_horizontal_material : reachable=false
@dimen/compat_button_inset_vertical_material : reachable=false
@dimen/compat_button_padding_horizontal_material : reachable=false
@dimen/compat_button_padding_vertical_material : reachable=false
@dimen/compat_control_corner_material : reachable=false
@dimen/compat_notification_large_icon_max_height : reachable=false
@dimen/compat_notification_large_icon_max_width : reachable=false
@dimen/notification_action_icon_size : reachable=false
@dimen/notification_action_text_size : reachable=false
@dimen/notification_big_circle_margin : reachable=false
@dimen/notification_content_margin_start : reachable=false
@dimen/notification_large_icon_height : reachable=false
@dimen/notification_large_icon_width : reachable=false
@dimen/notification_main_column_padding_top : reachable=false
@dimen/notification_media_narrow_margin : reachable=false
@dimen/notification_right_icon_size : reachable=false
@dimen/notification_right_side_padding_top : reachable=false
@dimen/notification_small_icon_background_padding : reachable=false
@dimen/notification_small_icon_size_as_large : reachable=false
@dimen/notification_subtext_size : reachable=false
@dimen/notification_top_pad : reachable=false
@dimen/notification_top_pad_large_text : reachable=false
@drawable/ic_call_answer : reachable=false
@drawable/ic_call_answer_low : reachable=false
@drawable/ic_call_answer_video : reachable=false
@drawable/ic_call_answer_video_low : reachable=false
@drawable/ic_call_decline : reachable=false
@drawable/ic_call_decline_low : reachable=false
@drawable/launch_background : reachable=false
@drawable/notification_action_background : reachable=false
    @color/androidx_core_ripple_material_light
    @dimen/compat_button_inset_horizontal_material
    @dimen/compat_button_inset_vertical_material
    @dimen/compat_control_corner_material
    @dimen/compat_button_padding_horizontal_material
    @dimen/compat_button_padding_vertical_material
@drawable/notification_bg : reachable=false
    @drawable/notification_bg_normal_pressed
    @drawable/notification_bg_normal
@drawable/notification_bg_low : reachable=false
    @drawable/notification_bg_low_pressed
    @drawable/notification_bg_low_normal
@drawable/notification_bg_low_normal : reachable=false
@drawable/notification_bg_low_pressed : reachable=false
@drawable/notification_bg_normal : reachable=false
@drawable/notification_bg_normal_pressed : reachable=false
@drawable/notification_icon_background : reachable=false
    @color/notification_icon_bg_color
@drawable/notification_oversize_large_icon_bg : reachable=false
@drawable/notification_template_icon_bg : reachable=false
@drawable/notification_template_icon_low_bg : reachable=false
@drawable/notification_tile_bg : reachable=false
    @drawable/notify_panel_notification_icon_bg
@drawable/notify_panel_notification_icon_bg : reachable=false
@id/accessibility_action_clickable_span : reachable=true
@id/accessibility_custom_action_0 : reachable=true
@id/accessibility_custom_action_1 : reachable=true
@id/accessibility_custom_action_10 : reachable=true
@id/accessibility_custom_action_11 : reachable=true
@id/accessibility_custom_action_12 : reachable=true
@id/accessibility_custom_action_13 : reachable=true
@id/accessibility_custom_action_14 : reachable=true
@id/accessibility_custom_action_15 : reachable=true
@id/accessibility_custom_action_16 : reachable=true
@id/accessibility_custom_action_17 : reachable=true
@id/accessibility_custom_action_18 : reachable=true
@id/accessibility_custom_action_19 : reachable=true
@id/accessibility_custom_action_2 : reachable=true
@id/accessibility_custom_action_20 : reachable=true
@id/accessibility_custom_action_21 : reachable=true
@id/accessibility_custom_action_22 : reachable=true
@id/accessibility_custom_action_23 : reachable=true
@id/accessibility_custom_action_24 : reachable=true
@id/accessibility_custom_action_25 : reachable=true
@id/accessibility_custom_action_26 : reachable=true
@id/accessibility_custom_action_27 : reachable=true
@id/accessibility_custom_action_28 : reachable=true
@id/accessibility_custom_action_29 : reachable=true
@id/accessibility_custom_action_3 : reachable=true
@id/accessibility_custom_action_30 : reachable=true
@id/accessibility_custom_action_31 : reachable=true
@id/accessibility_custom_action_4 : reachable=true
@id/accessibility_custom_action_5 : reachable=true
@id/accessibility_custom_action_6 : reachable=true
@id/accessibility_custom_action_7 : reachable=true
@id/accessibility_custom_action_8 : reachable=true
@id/accessibility_custom_action_9 : reachable=true
@id/action_container : reachable=true
@id/action_divider : reachable=true
@id/action_image : reachable=true
@id/action_text : reachable=true
@id/actions : reachable=true
@id/adjacent : reachable=false
@id/always : reachable=false
@id/alwaysAllow : reachable=false
@id/alwaysDisallow : reachable=false
@id/androidx_window_activity_scope : reachable=true
@id/async : reachable=false
@id/blocking : reachable=false
@id/bottomToTop : reachable=false
@id/chronometer : reachable=false
@id/dialog_button : reachable=false
@id/edit_text_id : reachable=false
@id/forever : reachable=false
@id/fragment_container_view_tag : reachable=false
@id/hide_ime_id : reachable=false
@id/icon : reachable=false
@id/icon_group : reachable=false
@id/info : reachable=true
@id/italic : reachable=false
@id/line1 : reachable=false
@id/line3 : reachable=false
@id/locale : reachable=true
@id/ltr : reachable=false
@id/never : reachable=false
@id/normal : reachable=false
@id/notification_background : reachable=false
@id/notification_main_column : reachable=false
@id/notification_main_column_container : reachable=false
@id/report_drawn : reachable=false
@id/right_icon : reachable=false
@id/right_side : reachable=false
@id/rtl : reachable=false
@id/special_effects_controller_view_tag : reachable=false
@id/tag_accessibility_actions : reachable=false
@id/tag_accessibility_clickable_spans : reachable=false
@id/tag_accessibility_heading : reachable=false
@id/tag_accessibility_pane_title : reachable=false
@id/tag_on_apply_window_listener : reachable=true
@id/tag_on_receive_content_listener : reachable=false
@id/tag_on_receive_content_mime_types : reachable=false
@id/tag_screen_reader_focusable : reachable=false
@id/tag_state_description : reachable=false
@id/tag_transition_group : reachable=false
@id/tag_unhandled_key_event_manager : reachable=false
@id/tag_unhandled_key_listeners : reachable=false
@id/tag_window_insets_animation_callback : reachable=true
@id/text : reachable=true
@id/text2 : reachable=true
@id/time : reachable=false
@id/title : reachable=false
@id/topToBottom : reachable=true
@id/view_tree_lifecycle_owner : reachable=true
@id/view_tree_on_back_pressed_dispatcher_owner : reachable=true
@id/view_tree_saved_state_registry_owner : reachable=true
@id/view_tree_view_model_store_owner : reachable=true
@id/visible_removing_fragment_view_tag : reachable=false
@integer/status_bar_notification_info_maxnum : reachable=false
@layout/custom_dialog : reachable=false
@layout/ime_base_split_test_activity : reachable=false
@layout/ime_secondary_split_test_activity : reachable=false
@layout/notification_action : reachable=false
    @style/Widget_Compat_NotificationActionContainer
    @dimen/notification_action_icon_size
    @style/Widget_Compat_NotificationActionText
@layout/notification_action_tombstone : reachable=false
    @style/Widget_Compat_NotificationActionContainer
    @dimen/notification_action_icon_size
    @style/Widget_Compat_NotificationActionText
@layout/notification_template_custom_big : reachable=false
    @dimen/notification_large_icon_width
    @dimen/notification_large_icon_height
    @layout/notification_template_icon_group
    @dimen/notification_right_side_padding_top
    @layout/notification_template_part_time
    @layout/notification_template_part_chronometer
    @style/TextAppearance_Compat_Notification_Info
@layout/notification_template_icon_group : reachable=false
    @dimen/notification_large_icon_width
    @dimen/notification_large_icon_height
    @dimen/notification_big_circle_margin
    @dimen/notification_right_icon_size
@layout/notification_template_part_chronometer : reachable=false
    @style/TextAppearance_Compat_Notification_Time
@layout/notification_template_part_time : reachable=false
    @style/TextAppearance_Compat_Notification_Time
@mipmap/ic_launcher : reachable=true
@string/androidx_startup : reachable=true
@string/call_notification_answer_action : reachable=false
@string/call_notification_answer_video_action : reachable=false
@string/call_notification_decline_action : reachable=false
@string/call_notification_hang_up_action : reachable=false
@string/call_notification_incoming_text : reachable=false
@string/call_notification_ongoing_text : reachable=false
@string/call_notification_screening_text : reachable=false
@string/status_bar_notification_info_overflow : reachable=false
@style/LaunchTheme : reachable=true
    @drawable/launch_background
@style/NormalTheme : reachable=true
@style/TextAppearance_Compat_Notification : reachable=false
@style/TextAppearance_Compat_Notification_Info : reachable=false
@style/TextAppearance_Compat_Notification_Line2 : reachable=false
    @style/TextAppearance_Compat_Notification_Info
@style/TextAppearance_Compat_Notification_Time : reachable=false
@style/TextAppearance_Compat_Notification_Title : reachable=false
@style/Widget_Compat_NotificationActionContainer : reachable=false
    @drawable/notification_action_background
@style/Widget_Compat_NotificationActionText : reachable=false
    @dimen/notification_action_text_size
    @color/androidx_core_secondary_text_default_material_light

The root reachable resources are:
 attr:activityAction:2130903040
 attr:activityName:2130903041
 attr:queryPatterns:2130903064
 attr:shortcutMatchRequired:2130903067
 color:androidx_core_ripple_material_light:2130968576
 color:androidx_core_secondary_text_default_material_light:2130968577
 id:accessibility_action_clickable_span:2131165184
 id:accessibility_custom_action_0:2131165185
 id:accessibility_custom_action_1:2131165186
 id:accessibility_custom_action_10:2131165187
 id:accessibility_custom_action_11:2131165188
 id:accessibility_custom_action_12:2131165189
 id:accessibility_custom_action_13:2131165190
 id:accessibility_custom_action_14:2131165191
 id:accessibility_custom_action_15:2131165192
 id:accessibility_custom_action_16:2131165193
 id:accessibility_custom_action_17:2131165194
 id:accessibility_custom_action_18:2131165195
 id:accessibility_custom_action_19:2131165196
 id:accessibility_custom_action_2:2131165197
 id:accessibility_custom_action_20:2131165198
 id:accessibility_custom_action_21:2131165199
 id:accessibility_custom_action_22:2131165200
 id:accessibility_custom_action_23:2131165201
 id:accessibility_custom_action_24:2131165202
 id:accessibility_custom_action_25:2131165203
 id:accessibility_custom_action_26:2131165204
 id:accessibility_custom_action_27:2131165205
 id:accessibility_custom_action_28:2131165206
 id:accessibility_custom_action_29:2131165207
 id:accessibility_custom_action_3:2131165208
 id:accessibility_custom_action_30:2131165209
 id:accessibility_custom_action_31:2131165210
 id:accessibility_custom_action_4:2131165211
 id:accessibility_custom_action_5:2131165212
 id:accessibility_custom_action_6:2131165213
 id:accessibility_custom_action_7:2131165214
 id:accessibility_custom_action_8:2131165215
 id:accessibility_custom_action_9:2131165216
 id:action_container:2131165217
 id:action_divider:2131165218
 id:action_image:2131165219
 id:action_text:2131165220
 id:actions:2131165221
 id:androidx_window_activity_scope:2131165226
 id:info:2131165238
 id:locale:2131165242
 id:tag_on_apply_window_listener:2131165258
 id:tag_window_insets_animation_callback:2131165266
 id:text:2131165267
 id:text2:2131165268
 id:topToBottom:2131165271
 id:view_tree_lifecycle_owner:2131165272
 id:view_tree_on_back_pressed_dispatcher_owner:2131165273
 id:view_tree_saved_state_registry_owner:2131165274
 id:view_tree_view_model_store_owner:2131165275
 mipmap:ic_launcher:2131361792
 string:androidx_startup:2131427328
 style:LaunchTheme:2131492864
 style:NormalTheme:2131492865
Unused resources are: 
 anim:fragment_fast_out_extra_slow_in:2130771968
 animator:fragment_close_enter:2130837504
 animator:fragment_close_exit:2130837505
 animator:fragment_fade_enter:2130837506
 animator:fragment_fade_exit:2130837507
 animator:fragment_open_enter:2130837508
 animator:fragment_open_exit:2130837509
 color:call_notification_answer_color:2130968578
 color:call_notification_decline_color:2130968579
 color:notification_action_color_filter:2130968580
 color:notification_icon_bg_color:2130968581
 dimen:compat_button_inset_horizontal_material:2131034112
 dimen:compat_button_inset_vertical_material:2131034113
 dimen:compat_button_padding_horizontal_material:2131034114
 dimen:compat_button_padding_vertical_material:2131034115
 dimen:compat_control_corner_material:2131034116
 dimen:compat_notification_large_icon_max_height:2131034117
 dimen:compat_notification_large_icon_max_width:2131034118
 dimen:notification_action_icon_size:2131034119
 dimen:notification_action_text_size:2131034120
 dimen:notification_big_circle_margin:2131034121
 dimen:notification_content_margin_start:2131034122
 dimen:notification_large_icon_height:2131034123
 dimen:notification_large_icon_width:2131034124
 dimen:notification_main_column_padding_top:2131034125
 dimen:notification_media_narrow_margin:2131034126
 dimen:notification_right_icon_size:2131034127
 dimen:notification_right_side_padding_top:2131034128
 dimen:notification_small_icon_background_padding:2131034129
 dimen:notification_small_icon_size_as_large:2131034130
 dimen:notification_subtext_size:2131034131
 dimen:notification_top_pad:2131034132
 dimen:notification_top_pad_large_text:2131034133
 drawable:ic_call_answer:2131099648
 drawable:ic_call_answer_low:2131099649
 drawable:ic_call_answer_video:2131099650
 drawable:ic_call_answer_video_low:2131099651
 drawable:ic_call_decline:2131099652
 drawable:ic_call_decline_low:2131099653
 drawable:notification_action_background:2131099655
 drawable:notification_bg:2131099656
 drawable:notification_bg_low:2131099657
 drawable:notification_bg_low_normal:2131099658
 drawable:notification_bg_low_pressed:2131099659
 drawable:notification_bg_normal:2131099660
 drawable:notification_bg_normal_pressed:2131099661
 drawable:notification_icon_background:2131099662
 drawable:notification_oversize_large_icon_bg:2131099663
 drawable:notification_template_icon_bg:2131099664
 drawable:notification_template_icon_low_bg:2131099665
 drawable:notification_tile_bg:2131099666
 drawable:notify_panel_notification_icon_bg:2131099667
 id:adjacent:2131165222
 id:always:2131165223
 id:alwaysAllow:2131165224
 id:alwaysDisallow:2131165225
 id:async:2131165227
 id:blocking:2131165228
 id:bottomToTop:2131165229
 id:chronometer:2131165230
 id:dialog_button:2131165231
 id:edit_text_id:2131165232
 id:forever:2131165233
 id:fragment_container_view_tag:2131165234
 id:hide_ime_id:2131165235
 id:icon:2131165236
 id:icon_group:2131165237
 id:italic:2131165239
 id:line1:2131165240
 id:line3:2131165241
 id:ltr:2131165243
 id:never:2131165244
 id:normal:2131165245
 id:notification_background:2131165246
 id:notification_main_column:2131165247
 id:notification_main_column_container:2131165248
 id:report_drawn:2131165249
 id:right_icon:2131165250
 id:right_side:2131165251
 id:rtl:2131165252
 id:special_effects_controller_view_tag:2131165253
 id:tag_accessibility_actions:2131165254
 id:tag_accessibility_clickable_spans:2131165255
 id:tag_accessibility_heading:2131165256
 id:tag_accessibility_pane_title:2131165257
 id:tag_on_receive_content_listener:2131165259
 id:tag_on_receive_content_mime_types:2131165260
 id:tag_screen_reader_focusable:2131165261
 id:tag_state_description:2131165262
 id:tag_transition_group:2131165263
 id:tag_unhandled_key_event_manager:2131165264
 id:tag_unhandled_key_listeners:2131165265
 id:time:2131165269
 id:title:2131165270
 id:visible_removing_fragment_view_tag:2131165276
 integer:status_bar_notification_info_maxnum:2131230720
 layout:custom_dialog:2131296256
 layout:ime_base_split_test_activity:2131296257
 layout:ime_secondary_split_test_activity:2131296258
 layout:notification_action:2131296259
 layout:notification_action_tombstone:2131296260
 layout:notification_template_custom_big:2131296261
 layout:notification_template_icon_group:2131296262
 layout:notification_template_part_chronometer:2131296263
 layout:notification_template_part_time:2131296264
 string:call_notification_answer_action:2131427329
 string:call_notification_answer_video_action:2131427330
 string:call_notification_decline_action:2131427331
 string:call_notification_hang_up_action:2131427332
 string:call_notification_incoming_text:2131427333
 string:call_notification_ongoing_text:2131427334
 string:call_notification_screening_text:2131427335
 string:status_bar_notification_info_overflow:2131427336
 style:TextAppearance_Compat_Notification:2131492866
 style:TextAppearance_Compat_Notification_Info:2131492867
 style:TextAppearance_Compat_Notification_Line2:2131492868
 style:TextAppearance_Compat_Notification_Time:2131492869
 style:TextAppearance_Compat_Notification_Title:2131492870
 style:Widget_Compat_NotificationActionContainer:2131492871
 style:Widget_Compat_NotificationActionText:2131492872
