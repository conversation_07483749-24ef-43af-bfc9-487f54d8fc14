import 'package:flutter/services.dart';
import 'package:sqflite/sqflite.dart';

final migrations = {
  1: '001_initial_schema.sql',
};

Future<void> applyMigrations(Database database, int from, int to) async {
  if (from >= to) {
    print('No migrations needed. Current version: $from, Target version: $to');
    return;
  }

  print('Applying migrations from version $from to $to');

  for (int version = from; version <= to; version++) {
    final migrationFile = migrations[version];

    if (migrationFile == null) {
      print('Migration file not found for version $version');
      continue;
    }

    try {
      final migrationPath = 'lib/migrations/$migrationFile';
      final migrationSql = await rootBundle.loadString(migrationPath);

      final statements = migrationSql.split(';').map((s) => s.trim()).toList();

      for (final statement in statements) {
        if (statement.trim().isNotEmpty) {
          await database.execute(statement);
        }
      }

      print('Successfully applied migration $version ($migrationFile)');
    } catch (e) {
      print('Error applying migration $version ($migrationFile): $e');
      rethrow;
    }
  }

  print('All migrations applied successfully. Database is now at version $to');
}
