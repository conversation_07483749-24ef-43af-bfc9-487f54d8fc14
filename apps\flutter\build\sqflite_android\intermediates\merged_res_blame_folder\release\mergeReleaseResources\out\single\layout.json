[{"merged": "com.tekartik.sqflite.sqflite_android-release-25:/layout/ime_base_split_test_activity.xml", "source": "com.tekartik.sqflite.sqflite_android-core-1.13.1-10:/layout/ime_base_split_test_activity.xml"}, {"merged": "com.tekartik.sqflite.sqflite_android-release-25:/layout/notification_action_tombstone.xml", "source": "com.tekartik.sqflite.sqflite_android-core-1.13.1-10:/layout/notification_action_tombstone.xml"}, {"merged": "com.tekartik.sqflite.sqflite_android-release-25:/layout/notification_template_part_chronometer.xml", "source": "com.tekartik.sqflite.sqflite_android-core-1.13.1-10:/layout/notification_template_part_chronometer.xml"}, {"merged": "com.tekartik.sqflite.sqflite_android-release-25:/layout/notification_template_icon_group.xml", "source": "com.tekartik.sqflite.sqflite_android-core-1.13.1-10:/layout/notification_template_icon_group.xml"}, {"merged": "com.tekartik.sqflite.sqflite_android-release-25:/layout/notification_template_part_time.xml", "source": "com.tekartik.sqflite.sqflite_android-core-1.13.1-10:/layout/notification_template_part_time.xml"}, {"merged": "com.tekartik.sqflite.sqflite_android-release-25:/layout/notification_template_custom_big.xml", "source": "com.tekartik.sqflite.sqflite_android-core-1.13.1-10:/layout/notification_template_custom_big.xml"}, {"merged": "com.tekartik.sqflite.sqflite_android-release-25:/layout/ime_secondary_split_test_activity.xml", "source": "com.tekartik.sqflite.sqflite_android-core-1.13.1-10:/layout/ime_secondary_split_test_activity.xml"}, {"merged": "com.tekartik.sqflite.sqflite_android-release-25:/layout/custom_dialog.xml", "source": "com.tekartik.sqflite.sqflite_android-core-1.13.1-10:/layout/custom_dialog.xml"}, {"merged": "com.tekartik.sqflite.sqflite_android-release-25:/layout/notification_action.xml", "source": "com.tekartik.sqflite.sqflite_android-core-1.13.1-10:/layout/notification_action.xml"}]