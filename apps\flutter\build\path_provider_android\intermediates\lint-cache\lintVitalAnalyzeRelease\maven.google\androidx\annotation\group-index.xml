<?xml version='1.0' encoding='UTF-8'?>
<androidx.annotation>
  <annotation versions="1.0.0-alpha1,1.0.0-alpha3,1.0.0-beta01,1.0.0-rc01,1.0.0-rc02,1.0.0,1.0.1,1.0.2,1.1.0-alpha01,1.1.0-alpha02,1.1.0-beta01,1.1.0-rc01,1.1.0,1.2.0-alpha01,1.2.0-beta01,1.2.0-rc01,1.2.0,1.3.0-alpha01,1.3.0-beta01,1.3.0-rc01,1.3.0,1.4.0-alpha01,1.4.0-alpha02,1.4.0-beta01,1.4.0-rc01,1.4.0,1.5.0-alpha01,1.5.0-alpha02,1.5.0-beta01,1.5.0-rc01,1.5.0,1.6.0-dev01,1.6.0-alpha01,1.6.0-beta01,1.6.0-rc01,1.6.0,1.7.0-alpha01,1.7.0-alpha02,1.7.0-alpha03,1.7.0-beta01,1.7.0-rc01,1.7.0,1.7.1,1.8.0-alpha01,1.8.0-alpha02,1.8.0-beta01,1.8.0-beta02,1.8.0-rc01,1.8.0,1.8.1,1.8.2,1.9.0-alpha01,1.9.0-alpha02,1.9.0-alpha03,1.9.0-beta01,1.9.0-rc01,1.9.0,1.9.1"/>
  <annotation-androidnativearm32 versions="1.8.0-alpha01,1.8.0-alpha02,1.8.0-beta01,1.8.0-beta02,1.8.0-rc01,1.8.0,1.8.1,1.8.2,1.9.0-alpha01,1.9.0-alpha02,1.9.0-alpha03,1.9.0-beta01,1.9.0-rc01,1.9.0,1.9.1"/>
  <annotation-androidnativearm64 versions="1.8.0-alpha01,1.8.0-alpha02,1.8.0-beta01,1.8.0-beta02,1.8.0-rc01,1.8.0,1.8.1,1.8.2,1.9.0-alpha01,1.9.0-alpha02,1.9.0-alpha03,1.9.0-beta01,1.9.0-rc01,1.9.0,1.9.1"/>
  <annotation-androidnativex64 versions="1.8.0-alpha01,1.8.0-alpha02,1.8.0-beta01,1.8.0-beta02,1.8.0-rc01,1.8.0,1.8.1,1.8.2,1.9.0-alpha01,1.9.0-alpha02,1.9.0-alpha03,1.9.0-beta01,1.9.0-rc01,1.9.0,1.9.1"/>
  <annotation-androidnativex86 versions="1.8.0-alpha01,1.8.0-alpha02,1.8.0-beta01,1.8.0-beta02,1.8.0-rc01,1.8.0,1.8.1,1.8.2,1.9.0-alpha01,1.9.0-alpha02,1.9.0-alpha03,1.9.0-beta01,1.9.0-rc01,1.9.0,1.9.1"/>
  <annotation-experimental versions="1.0.0-alpha01,1.0.0-beta01,1.0.0-rc01,1.0.0,1.1.0-alpha01,1.1.0-beta01,1.1.0-rc01,1.1.0-rc02,1.1.0,1.2.0-alpha01,1.2.0-beta01,1.2.0-rc01,1.2.0,1.3.0-alpha01,1.3.0-beta01,1.3.0-rc01,1.3.0,1.3.1,1.4.0-dev01,1.4.0-alpha01,1.4.0-beta01,1.4.0-rc01,1.4.0,1.4.1,1.5.0-alpha01,1.5.0-beta01,1.5.0-rc01,1.5.0"/>
  <annotation-experimental-lint versions="1.0.0-alpha01,1.0.0-beta01,1.0.0-rc01,1.0.0"/>
  <annotation-iosarm64 versions="1.6.0-dev01,1.7.0-alpha01,1.7.0-alpha02,1.7.0-alpha03,1.7.0-beta01,1.7.0-rc01,1.7.0,1.7.1,1.8.0-alpha01,1.8.0-alpha02,1.8.0-beta01,1.8.0-beta02,1.8.0-rc01,1.8.0,1.8.1,1.8.2,1.9.0-alpha01,1.9.0-alpha02,1.9.0-alpha03,1.9.0-beta01,1.9.0-rc01,1.9.0,1.9.1"/>
  <annotation-iossimulatorarm64 versions="1.6.0-dev01,1.7.0-alpha01,1.7.0-alpha02,1.7.0-alpha03,1.7.0-beta01,1.7.0-rc01,1.7.0,1.7.1,1.8.0-alpha01,1.8.0-alpha02,1.8.0-beta01,1.8.0-beta02,1.8.0-rc01,1.8.0,1.8.1,1.8.2,1.9.0-alpha01,1.9.0-alpha02,1.9.0-alpha03,1.9.0-beta01,1.9.0-rc01,1.9.0,1.9.1"/>
  <annotation-iosx64 versions="1.6.0-dev01,1.7.0-alpha01,1.7.0-alpha02,1.7.0-alpha03,1.7.0-beta01,1.7.0-rc01,1.7.0,1.7.1,1.8.0-alpha01,1.8.0-alpha02,1.8.0-beta01,1.8.0-beta02,1.8.0-rc01,1.8.0,1.8.1,1.8.2,1.9.0-alpha01,1.9.0-alpha02,1.9.0-alpha03,1.9.0-beta01,1.9.0-rc01,1.9.0,1.9.1"/>
  <annotation-js versions="1.9.1"/>
  <annotation-jvm versions="1.6.0-dev01,1.6.0-alpha01,1.6.0-beta01,1.6.0-rc01,1.6.0,1.7.0-alpha01,1.7.0-alpha02,1.7.0-alpha03,1.7.0-beta01,1.7.0-rc01,1.7.0,1.7.1,1.8.0-alpha01,1.8.0-alpha02,1.8.0-beta01,1.8.0-beta02,1.8.0-rc01,1.8.0,1.8.1,1.8.2,1.9.0-alpha01,1.9.0-alpha02,1.9.0-alpha03,1.9.0-beta01,1.9.0-rc01,1.9.0,1.9.1"/>
  <annotation-linuxarm64 versions="1.8.1,1.8.2,1.9.0-alpha01,1.9.0-alpha02,1.9.0-alpha03,1.9.0-beta01,1.9.0-rc01,1.9.0,1.9.1"/>
  <annotation-linuxx64 versions="1.6.0-dev01,1.7.0-alpha01,1.7.0-alpha02,1.7.0-alpha03,1.7.0-beta01,1.7.0-rc01,1.7.0,1.7.1,1.8.0-alpha01,1.8.0-alpha02,1.8.0-beta01,1.8.0-beta02,1.8.0-rc01,1.8.0,1.8.1,1.8.2,1.9.0-alpha01,1.9.0-alpha02,1.9.0-alpha03,1.9.0-beta01,1.9.0-rc01,1.9.0,1.9.1"/>
  <annotation-macosarm64 versions="1.6.0-dev01,1.7.0-alpha01,1.7.0-alpha02,1.7.0-alpha03,1.7.0-beta01,1.7.0-rc01,1.7.0,1.7.1,1.8.0-alpha01,1.8.0-alpha02,1.8.0-beta01,1.8.0-beta02,1.8.0-rc01,1.8.0,1.8.1,1.8.2,1.9.0-alpha01,1.9.0-alpha02,1.9.0-alpha03,1.9.0-beta01,1.9.0-rc01,1.9.0,1.9.1"/>
  <annotation-macosx64 versions="1.6.0-dev01,1.7.0-alpha01,1.7.0-alpha02,1.7.0-alpha03,1.7.0-beta01,1.7.0-rc01,1.7.0,1.7.1,1.8.0-alpha01,1.8.0-alpha02,1.8.0-beta01,1.8.0-beta02,1.8.0-rc01,1.8.0,1.8.1,1.8.2,1.9.0-alpha01,1.9.0-alpha02,1.9.0-alpha03,1.9.0-beta01,1.9.0-rc01,1.9.0,1.9.1"/>
  <annotation-mingwx64 versions="1.8.2,1.9.0-alpha02,1.9.0-alpha03,1.9.0-beta01,1.9.0-rc01,1.9.0,1.9.1"/>
  <annotation-tvosarm64 versions="1.8.1,1.8.2,1.9.0-alpha02,1.9.0-alpha03,1.9.0-beta01,1.9.0-rc01,1.9.0,1.9.1"/>
  <annotation-tvossimulatorarm64 versions="1.8.1,1.8.2,1.9.0-alpha02,1.9.0-alpha03,1.9.0-beta01,1.9.0-rc01,1.9.0,1.9.1"/>
  <annotation-tvosx64 versions="1.8.1,1.8.2,1.9.0-alpha02,1.9.0-alpha03,1.9.0-beta01,1.9.0-rc01,1.9.0,1.9.1"/>
  <annotation-wasm-js versions="1.9.0-alpha02,1.9.0-alpha03,1.9.0-beta01,1.9.0-rc01,1.9.0,1.9.1"/>
  <annotation-watchosarm32 versions="1.8.1,1.8.2,1.9.0-alpha02,1.9.0-alpha03,1.9.0-beta01,1.9.0-rc01,1.9.0,1.9.1"/>
  <annotation-watchosarm64 versions="1.8.1,1.8.2,1.9.0-alpha02,1.9.0-alpha03,1.9.0-beta01,1.9.0-rc01,1.9.0,1.9.1"/>
  <annotation-watchosdevicearm64 versions="1.9.0-beta01,1.9.0-rc01,1.9.0,1.9.1"/>
  <annotation-watchossimulatorarm64 versions="1.8.1,1.8.2,1.9.0-alpha02,1.9.0-alpha03,1.9.0-beta01,1.9.0-rc01,1.9.0,1.9.1"/>
  <annotation-watchosx64 versions="1.8.1,1.8.2,1.9.0-alpha02,1.9.0-alpha03,1.9.0-beta01,1.9.0-rc01,1.9.0,1.9.1"/>
</androidx.annotation>
