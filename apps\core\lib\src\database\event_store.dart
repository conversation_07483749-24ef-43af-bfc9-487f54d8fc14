import 'dart:async';

import 'package:core/core.dart';
import 'package:uuid/uuid.dart';

/// High-level event store for managing domain events
class EventStore {
  final DatabaseManager _databaseManager;
  final String _nodeId;
  final StreamController<Event> _eventController =
      StreamController<Event>.broadcast();

  /// Stream of new events as they are added
  Stream<Event> get eventStream => _eventController.stream;

  EventStore(this._databaseManager, this._nodeId);

  /// Create and store a new event
  Future<Event> createEvent({
    required String entityType,
    required String entityId,
    required String eventType,
    required Map<String, dynamic> payload,
  }) async {
    const uuid = Uuid();
    final event = Event(
      id: uuid.v4(),
      origin: _nodeId,
      timestamp: DateTime.now(),
      entityType: entityType,
      entityId: entityId,
      eventType: eventType,
      payload: payload,
      synced: false,
    );

    await _databaseManager.insertEvent(event);
    _eventController.add(event);

    return event;
  }

  /// Store events received from other nodes
  Future<void> storeReceivedEvents(List<Event> events) async {
    if (events.isEmpty) return;

    // Filter out events that originated from this node
    final externalEvents =
        events.where((event) => event.origin != _nodeId).toList();

    if (externalEvents.isNotEmpty) {
      await _databaseManager.insertEvents(externalEvents);

      // Notify listeners of new events
      for (final event in externalEvents) {
        _eventController.add(event);
      }
    }
  }

  /// Get all events for a specific entity to reconstruct its state
  Future<List<Event>> getEntityHistory(
      String entityType, String entityId) async {
    return await _databaseManager.getEventsByEntity(entityType, entityId);
  }

  /// Get unsynced events for transmission to other nodes
  Future<List<Event>> getUnsyncedEvents() async {
    return await _databaseManager.getUnsyncedEvents();
  }

  /// Mark events as successfully synced
  Future<void> markEventsSynced(List<String> eventIds) async {
    await _databaseManager.markEventsSynced(eventIds);
  }

  /// Reconstruct entity state from events (example for tasks)
  Map<String, dynamic>? reconstructTaskState(
      String taskId, List<Event> events) {
    if (events.isEmpty) return null;

    Map<String, dynamic> state = {};

    for (final event in events) {
      switch (event.eventType) {
        case 'task_created':
          state = {
            'id': taskId,
            'title': event.payload['title'],
            'description': event.payload['description'] ?? '',
            'completed': false,
            'created_at': event.timestamp.toIso8601String(),
            'updated_at': event.timestamp.toIso8601String(),
          };
          break;

        case 'task_updated':
          state.addAll(event.payload);
          state['updated_at'] = event.timestamp.toIso8601String();
          break;

        case 'task_completed':
          state['completed'] = true;
          state['completed_at'] = event.timestamp.toIso8601String();
          state['updated_at'] = event.timestamp.toIso8601String();
          break;

        case 'task_deleted':
          return null; // Task was deleted
      }
    }

    return state.isEmpty ? null : state;
  }

  /// Get all current tasks by reconstructing from events
  Future<List<Map<String, dynamic>>> getCurrentTasks() async {
    final allEvents = await _databaseManager.getAllEvents();
    final taskEvents =
        allEvents.where((event) => event.entityType == 'task').toList();

    // Group events by task ID
    final Map<String, List<Event>> taskEventGroups = {};
    for (final event in taskEvents) {
      taskEventGroups.putIfAbsent(event.entityId, () => []).add(event);
    }

    // Reconstruct each task's current state
    final List<Map<String, dynamic>> tasks = [];
    for (final entry in taskEventGroups.entries) {
      final taskState = reconstructTaskState(entry.key, entry.value);
      if (taskState != null) {
        tasks.add(taskState);
      }
    }

    // Sort by creation date
    tasks.sort((a, b) =>
        (a['created_at'] as String).compareTo(b['created_at'] as String));

    return tasks;
  }

  /// Dispose resources
  void dispose() {
    _eventController.close();
  }
}
