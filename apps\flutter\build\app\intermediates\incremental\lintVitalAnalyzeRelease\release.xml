<variant
    name="release"
    package="com.itmarck.drift"
    minSdkVersion="21"
    targetSdkVersion="35"
    shrinking="true"
    mergedManifest="D:\code\drift\apps\flutter\build\app\intermediates\merged_manifest\release\processReleaseMainManifest\AndroidManifest.xml"
    proguardFiles="D:\code\drift\apps\flutter\build\app\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.7.3;D:\sdk\flutter\packages\flutter_tools\gradle\flutter_proguard_rules.pro;proguard-rules.pro"
    partialResultsDir="D:\code\drift\apps\flutter\build\app\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
    desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.12\transforms\7ff7c222f72a39cd2d6660f4156e5aff\transformed\D8BackportedDesugaredMethods.txt">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <manifestPlaceholders>
    <placeholder
        name="applicationName"
        value="android.app.Application" />
  </manifestPlaceholders>
  <artifact
      classOutputs="D:\code\drift\apps\flutter\build\app\intermediates\javac\release\compileReleaseJavaWithJavac\classes;D:\code\drift\apps\flutter\build\app\tmp\kotlin-classes\release;D:\code\drift\apps\flutter\build\app\kotlinToolingMetadata;D:\code\drift\apps\flutter\build\app\intermediates\compile_and_runtime_not_namespaced_r_class_jar\release\processReleaseResources\R.jar"
      type="MAIN"
      applicationId="com.itmarck.drift"
      generatedSourceFolders="D:\code\drift\apps\flutter\build\app\generated\ap_generated_sources\release\out"
      generatedResourceFolders="D:\code\drift\apps\flutter\build\app\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.12\transforms\7ff7c222f72a39cd2d6660f4156e5aff\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
