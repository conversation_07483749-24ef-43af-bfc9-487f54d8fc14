-- Entity type (e.g. task, note, etc.)
CREATE TABLE types (
  id TEXT PRIMARY KEY,
  label TEXT
);

INSERT INTO types
(id) VALUES
('task'),
('note');

CREATE TABLE fields (
  id TEXT PRIMARY KEY, -- 'title', 'status', etc.
  type_id TEXT NOT NULL, -- FK to types.id
  field_type TEXT NOT NULL, -- 'text', 'number', 'date', etc.
  required INTEGER DEFAULT 0, -- 1 if required
  FOREIGN KEY (type_id) REFERENCES types(id)
);

INSERT INTO fields
(id,             type_id,    field_type,  required) VALUES
('title',       'task',     'text',       1),
('description', 'task',     'text',       0),
('done',        'task',     'boolean',    0),
('due',         'task',     'date',       0);


-- Entities
CREATE TABLE entities (
  id TEXT PRIMARY KEY, -- UUID
  type_id TEXT NOT NULL, -- FK to types.id
  created_at INTEGER NOT NULL, -- UTC timestamp
  FOREIGN KEY (type_id) REFERENCES types(id)
);

-- Events represent a single operation that affects one or more fields
CREATE TABLE events (
  id TEXT PRIMARY KEY, -- UUID
  type TEXT NOT NULL, -- 'create', 'update', 'delete', etc.
  entity_id TEXT NOT NULL, -- FK to entities.id
  node TEXT NOT NULL, -- origin node
  created_at INTEGER NOT NULL, -- UTC timestamp
  FOREIGN KEY (entity_id) REFERENCES entities(id)
);

-- Records represent individual field changes made by an event
CREATE TABLE records (
  event_id TEXT NOT NULL, -- FK to events.id
  field_id TEXT NOT NULL, -- FK to fields.id
  value TEXT, -- new value (can be NULL)
  created_at INTEGER NOT NULL,
  PRIMARY KEY (event_id, field_id),
  FOREIGN KEY (event_id) REFERENCES events(id),
  FOREIGN KEY (field_id) REFERENCES fields(id)
);

-- Local Registry
CREATE TABLE logs (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  level TEXT NOT NULL, -- 'info', 'warn', 'error', etc.
  message TEXT NOT NULL,
  context TEXT, -- JSON with optional metadata
  created_at INTEGER NOT NULL -- UTC timestamp
);

-- Profile information (one row only)
CREATE TABLE profiles (
  id INTEGER PRIMARY KEY CHECK (id = 1),
  display_name TEXT DEFAULT 'Local',
  node TEXT, -- UUID generated for this device
  ts_tailnet TEXT, -- Tailscale tailnet name
  ts_token TEXT -- Tailscale access token
);

INSERT INTO profiles DEFAULT VALUES;
