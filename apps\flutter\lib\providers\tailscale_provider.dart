import 'package:flutter/foundation.dart';
import 'package:core/core.dart';

/// Provider for managing Tailscale configuration and device discovery
class <PERSON><PERSON><PERSON><PERSON>rov<PERSON> extends ChangeNotifier {
  final Core _core;
  
  String _tailnet = '';
  String _token = '';
  List<Node> _devices = [];
  bool _isLoading = false;
  String? _error;
  
  TailscaleProvider(this._core) {
    _loadConfiguration();
  }
  
  /// Get current tailnet
  String get tailnet => _tailnet;
  
  /// Get current token (masked for display)
  String get tokenMasked => _token.isNotEmpty 
      ? '${_token.substring(0, 8)}...' 
      : '';
  
  /// Get list of devices
  List<Node> get devices => List.unmodifiable(_devices);
  
  /// Get loading state
  bool get isLoading => _isLoading;
  
  /// Get error message
  String? get error => _error;
  
  /// Check if configuration is complete
  bool get isConfigured => _tailnet.isNotEmpty && _token.isNotEmpty;
  
  /// Load configuration from database
  Future<void> _loadConfiguration() async {
    try {
      final profile = await _core.database.getProfile();
      if (profile != null) {
        _tailnet = profile['ts_tailnet'] as String? ?? '';
        _token = profile['ts_token'] as String? ?? '';
        notifyListeners();
        
        // Load devices if configured
        if (isConfigured) {
          await _loadDevices();
        }
      }
    } catch (e) {
      _setError('Failed to load configuration: $e');
    }
  }
  
  /// Update Tailscale configuration
  Future<void> updateConfiguration(String tailnet, String token) async {
    _setLoading(true);
    _setError(null);
    
    try {
      // Save to database
      await _core.database.updateProfile(
        tsTailnet: tailnet,
        tsToken: token,
      );
      
      _tailnet = tailnet;
      _token = token;
      
      // Load devices with new configuration
      await _loadDevices();
      
    } catch (e) {
      _setError('Failed to update configuration: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  /// Load devices from Tailscale API
  Future<void> _loadDevices() async {
    if (!isConfigured) return;
    
    _setLoading(true);
    _setError(null);
    
    try {
      final client = TailscaleClient(
        accessToken: _token,
        tailnet: _tailnet,
      );
      
      _devices = await client.getDevices();
      
    } catch (e) {
      _setError('Failed to load devices: $e');
      _devices = [];
    } finally {
      _setLoading(false);
    }
  }
  
  /// Refresh devices list
  Future<void> refreshDevices() async {
    await _loadDevices();
  }
  
  /// Test connection to Tailscale API
  Future<bool> testConnection() async {
    if (!isConfigured) return false;
    
    try {
      final client = TailscaleClient(
        accessToken: _token,
        tailnet: _tailnet,
      );
      
      return await client.testConnection();
    } catch (e) {
      return false;
    }
  }
  
  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  /// Set error message
  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }
}
