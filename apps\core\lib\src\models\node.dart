import 'package:core/src/configuration.dart';

/// Represents a node in the network
class Node {
  final String id;
  final String name;
  final String ipAddress;
  final int port;
  final bool isOnline;
  final DateTime lastSeen;

  const Node({
    required this.id,
    required this.name,
    required this.ipAddress,
    required this.port,
    required this.isOnline,
    required this.lastSeen,
  });

  /// Create a Node from Tailscale API response
  factory Node.fromTailscaleDevice(
    Map<String, dynamic> device, [
    int port = Configuration.defaultApiPort,
  ]) {
    final addresses = device['addresses'] as List<dynamic>? ?? [];
    final ipAddress = addresses.isNotEmpty ? addresses.first as String : '';

    return Node(
      id: device['id'] as String,
      name: device['name'] as String,
      ipAddress: ipAddress,
      port: port,
      isOnline: true,
      lastSeen: DateTime.parse(
          device['lastSeen'] as String? ?? DateTime.now().toIso8601String()),
    );
  }

  /// Create from JSON
  factory Node.fromJson(Map<String, dynamic> json) {
    return Node(
      id: json['id'] as String,
      name: json['name'] as String,
      ipAddress: json['ip_address'] as String,
      port: json['port'] as int,
      isOnline: json['is_online'] as bool,
      lastSeen: DateTime.parse(json['last_seen'] as String),
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'ip_address': ipAddress,
      'port': port,
      'is_online': isOnline,
      'last_seen': lastSeen.toIso8601String(),
    };
  }

  /// Get the base URL for this node's API
  String get baseUrl => 'http://$ipAddress:$port';

  /// Create a copy with updated fields
  Node copyWith({
    String? id,
    String? name,
    String? ipAddress,
    int? port,
    bool? isOnline,
    DateTime? lastSeen,
  }) {
    return Node(
      id: id ?? this.id,
      name: name ?? this.name,
      ipAddress: ipAddress ?? this.ipAddress,
      port: port ?? this.port,
      isOnline: isOnline ?? this.isOnline,
      lastSeen: lastSeen ?? this.lastSeen,
    );
  }

  @override
  String toString() {
    return 'Node(id: $id, name: $name, ipAddress: $ipAddress, '
        'port: $port, isOnline: $isOnline, lastSeen: $lastSeen)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Node && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
