[{"merged": "com.tekartik.sqflite.sqflite_android-release-25:/animator/fragment_close_exit.xml", "source": "com.tekartik.sqflite.sqflite_android-fragment-1.7.1-2:/animator/fragment_close_exit.xml"}, {"merged": "com.tekartik.sqflite.sqflite_android-release-25:/animator/fragment_fade_enter.xml", "source": "com.tekartik.sqflite.sqflite_android-fragment-1.7.1-2:/animator/fragment_fade_enter.xml"}, {"merged": "com.tekartik.sqflite.sqflite_android-release-25:/animator/fragment_open_exit.xml", "source": "com.tekartik.sqflite.sqflite_android-fragment-1.7.1-2:/animator/fragment_open_exit.xml"}, {"merged": "com.tekartik.sqflite.sqflite_android-release-25:/animator/fragment_fade_exit.xml", "source": "com.tekartik.sqflite.sqflite_android-fragment-1.7.1-2:/animator/fragment_fade_exit.xml"}, {"merged": "com.tekartik.sqflite.sqflite_android-release-25:/animator/fragment_open_enter.xml", "source": "com.tekartik.sqflite.sqflite_android-fragment-1.7.1-2:/animator/fragment_open_enter.xml"}, {"merged": "com.tekartik.sqflite.sqflite_android-release-25:/animator/fragment_close_enter.xml", "source": "com.tekartik.sqflite.sqflite_android-fragment-1.7.1-2:/animator/fragment_close_enter.xml"}]