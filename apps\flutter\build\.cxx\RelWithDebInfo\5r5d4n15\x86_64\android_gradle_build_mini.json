{"buildFiles": ["D:\\sdk\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\code\\drift\\apps\\flutter\\build\\.cxx\\RelWithDebInfo\\5r5d4n15\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\code\\drift\\apps\\flutter\\build\\.cxx\\RelWithDebInfo\\5r5d4n15\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}