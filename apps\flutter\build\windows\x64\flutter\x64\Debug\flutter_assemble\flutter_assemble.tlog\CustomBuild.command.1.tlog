^D:\CODE\DRIFT\APPS\FLUTTER\BUILD\WINDOWS\X64\CMAKEFILES\95A2AB58F57FAF6A64FF87485B71B35D\FLUTTER_WINDOWS.DLL.RULE
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=D:\sdk\flutter PROJECT_DIR=D:\code\drift\apps\flutter FLUTTER_ROOT=D:\sdk\flutter FLUTTER_EPHEMERAL_DIR=D:\code\drift\apps\flutter\windows\flutter\ephemeral PROJECT_DIR=D:\code\drift\apps\flutter FLUTTER_TARGET=D:\code\drift\apps\flutter\lib\main.dart DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzIuNQ==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049ZmNmMmMxMTU3Mg==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049ZGQ5M2RlNmZiMQ==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjE= DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=D:\code\drift\apps\flutter\.dart_tool\package_config.json D:/sdk/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\CODE\DRIFT\APPS\FLUTTER\BUILD\WINDOWS\X64\CMAKEFILES\E8481BEDDBDB619E5D423F3363E0610F\FLUTTER_ASSEMBLE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\CODE\DRIFT\APPS\FLUTTER\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/code/drift/apps/flutter/windows -BD:/code/drift/apps/flutter/build/windows/x64 --check-stamp-file D:/code/drift/apps/flutter/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
